cc.Class({
    extends: cc.Component,

    properties: {
        // 玩家节点
        player: cc.Node,

        // 平台管理器
        platformManager: cc.Node,

        // 金币管理器
        coinManager: cc.Node,

        // 分数管理器
        scoreManager: cc.Node,

        // 背景管理器
        backgroundManager: cc.Node,

        // 暂停按钮
        pauseButton: cc.Node,

        // 暂停页面
        pauseAdvert: cc.Node,

        // 继续游戏按钮
        resumeButton: cc.Node,

        // 初始所有东西向左移动速度
        startSpeed: 200,

        // 速度增加率（每秒）
        speedIncreaseRate: 10,

        // 最大速度
        maxSpeed: 500
    },

    onLoad() {
        // 初始化物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        physicsManager.enabled = true;
        physicsManager.gravity = cc.v2(0, -800); // 设置重力

        cc.director.getPhysicsManager().debugDrawFlags = cc.PhysicsManager.DrawBits.e_aabbBit |
    cc.PhysicsManager.DrawBits.e_pairBit |
    cc.PhysicsManager.DrawBits.e_centerOfMassBit |
    cc.PhysicsManager.DrawBits.e_jointBit |
    cc.PhysicsManager.DrawBits.e_shapeBit
    ;
        
        // 初始化游戏状态
        this.isGameOver = false;
        this.isPaused = false;
        
        // 初始化距离
        this.distance = 0;

        // 获取平台管理器组件
        this.platformManagerComp = this.platformManager.getComponent('PlatformManager');

        // 获取金币管理器组件
        if (this.coinManager) {
            this.coinManagerComp = this.coinManager.getComponent('CoinMgr');
        }

        // 获取玩家组件
        if (this.player) {
            this.playerComp = this.player.getComponent('player1');
        }

        // 获取分数管理器组件
        if (this.scoreManager) {
            this.scoreManagerComp = this.scoreManager.getComponent('ScoreManager');
        }

        // 获取背景管理器组件
        if (this.backgroundManager) {
            this.backgroundManagerComp = this.backgroundManager.getComponent('BackgroundManager');
        }

        // 初始化暂停功能
        this.initPauseSystem();
    },

    initPauseSystem() {
        // 初始时隐藏暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = false;
        }

        // 绑定暂停按钮点击事件
        if (this.pauseButton) {
            this.pauseButton.on('click', this.onPauseButtonClick, this);
        }

        // 绑定继续游戏按钮点击事件
        if (this.resumeButton) {
            this.resumeButton.on('click', this.onResumeButtonClick, this);
        }
    },

    start() {
        // 设置初始速度
        this.currentSpeed = this.startSpeed;
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.backgroundManagerComp) {
            this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3); // 背景滚动速度稍慢
        }


    },

    update(dt) {
        if (this.isGameOver) return;

        // 增加距离（仅用于速度计算，不影响分数）
        this.distance += this.currentSpeed * dt;

        // 逐渐增加速度
        this.currentSpeed = Math.min(this.maxSpeed, this.currentSpeed + this.speedIncreaseRate * dt);
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.backgroundManagerComp) {
            this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3); // 背景滚动速度稍慢
        }

        // 检查游戏是否结束
        this.checkGameOver();
    },
    
    
    checkGameOver() {
        // 如果玩家组件报告游戏结束
        // if (this.playerComp&&this.playerComp.isGameOver) {
        //     this.gameOver();
        // }
        
        // 或者检查玩家是否掉出了屏幕
        if (this.player && this.player.y < -cc.winSize.height / 2 - 100) {
            this.gameOver();
        }
    },
    
    gameOver() {
        if (this.isGameOver) return; // 防止多次调用

        this.isGameOver = true;

        // 通知分数管理器游戏结束
        if (this.scoreManagerComp) {
            this.scoreManagerComp.onGameOver();
        }

        // 停止平台移动
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(0);
        }

        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(0);
        }

        // 停止背景滚动
        if (this.backgroundManagerComp) {
            this.backgroundManagerComp.setScrollSpeed(0);
        }

        // 这里可以添加游戏结束UI的显示
        // ...

        // 延迟几秒后显示重新开始按钮
        this.scheduleOnce(() => {
            // 显示重新开始按钮
            // ...
        }, 2);
    },

    // 暂停按钮点击事件
    onPauseButtonClick() {
        console.log("暂停按钮被点击");
        this.pauseGame();
    },

    // 继续游戏按钮点击事件
    onResumeButtonClick() {
        console.log("继续游戏按钮被点击");
        this.resumeGame();
    },

    // 暂停游戏
    pauseGame() {
        if (this.isGameOver || this.isPaused) return;

        console.log("游戏暂停");
        this.isPaused = true;

        // 暂停物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        if (physicsManager) {
            physicsManager.enabled = false;
        }

        // 暂停所有游戏对象的移动（通过设置速度为0）
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(0);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(0);
        }
        if (this.backgroundManagerComp) {
            this.backgroundManagerComp.setScrollSpeed(0);
        }

        // 显示暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = true;
        }
    },

    // 继续游戏
    resumeGame() {
        if (this.isGameOver || !this.isPaused) return;

        console.log("游戏继续");
        this.isPaused = false;

        // 恢复物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        if (physicsManager) {
            physicsManager.enabled = true;
        }

        // 恢复所有游戏对象的移动速度
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.backgroundManagerComp) {
            this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3);
        }

        // 隐藏暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = false;
        }
    },

    onDestroy() {
        // 移除事件监听
        if (this.pauseButton) {
            this.pauseButton.off('click', this.onPauseButtonClick, this);
        }
        if (this.resumeButton) {
            this.resumeButton.off('click', this.onResumeButtonClick, this);
        }
    }
});
